import React, { useState, useEffect } from 'react';
import axios from 'axios';

export const TrialBalance = () => {
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [trialBalanceData, setTrialBalanceData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showStatement, setShowStatement] = useState(false);

  // Set default dates on component mount
  useEffect(() => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const januaryFirst = `${currentYear}-01-01`;
    const today = currentDate.toISOString().split('T')[0];

    setFromDate(januaryFirst);
    setToDate(today);
  }, []);

  const handleGenerateReport = async () => {
    if (!fromDate || !toDate) {
      setError('Please select both from and to dates');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await axios.post('/api/trial-balance', {
        from_date: fromDate,
        to_date: toDate,
      });

      if (response.data.success) {
        setTrialBalanceData(response.data.data);
        setShowStatement(true);
      } else {
        throw new Error(response.data.message || 'Failed to generate trial balance');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to generate trial balance. Please try again.');
      console.error('Error generating trial balance:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePrintStatement = () => {
    if (!trialBalanceData) return;

    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  const generatePrintContent = () => {
    if (!trialBalanceData) return '';

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Trial Balance</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .date-range { text-align: center; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .amount { text-align: right; }
            .total-row { background-color: #f0f0f0; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Trial Balance</h1>
          </div>
          <div class="date-range">
            <p>From: ${fromDate} To: ${toDate}</p>
          </div>
          ${generateTrialBalanceTable()}
        </body>
      </html>
    `;
  };

  const generateTrialBalanceTable = () => {
    if (!trialBalanceData) return '';

    const { accounts, totals } = trialBalanceData;

    let tableRows = '';
    accounts.forEach(account => {
      tableRows += `
        <tr>
          <td>${account.account_name}</td>
          <td class="amount">${account.debit > 0 ? account.debit.toLocaleString() : '-'}</td>
          <td class="amount">${account.credit > 0 ? account.credit.toLocaleString() : '-'}</td>
        </tr>
      `;
    });

    return `
      <table>
        <thead>
          <tr>
            <th style="width: 60%">Account Name</th>
            <th style="width: 20%">Debit (Rs.)</th>
            <th style="width: 20%">Credit (Rs.)</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
          <tr class="total-row">
            <td><strong>Total</strong></td>
            <td class="amount"><strong>${totals.total_debit.toLocaleString()}</strong></td>
            <td class="amount"><strong>${totals.total_credit.toLocaleString()}</strong></td>
          </tr>
        </tbody>
      </table>
    `;
  };

  return (
    <div className="container p-6 mx-auto">
      <div className="p-6 bg-white rounded-lg shadow-md">
        <h1 className="mb-6 text-2xl font-bold text-gray-800">Trial Balance</h1>

        {/* Date Selection */}
        <div className="grid grid-cols-1 gap-4 mb-6 md:grid-cols-3">
          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              From Date
            </label>
            <input
              type="date"
              value={fromDate}
              onChange={(e) => setFromDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block mb-2 text-sm font-medium text-gray-700">
              To Date
            </label>
            <input
              type="date"
              value={toDate}
              onChange={(e) => setToDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex items-end">
            <button
              onClick={handleGenerateReport}
              disabled={loading}
              className="w-full px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {loading ? 'Generating...' : 'Generate Report'}
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="px-4 py-3 mb-4 text-red-700 bg-red-100 border border-red-400 rounded">
            {error}
          </div>
        )}

        {/* Print Button */}
        {showStatement && trialBalanceData && (
          <div className="mb-4">
            <button
              onClick={handlePrintStatement}
              className="px-4 py-2 text-white bg-green-600 rounded-md hover:bg-green-700"
            >
              Print Statement
            </button>
          </div>
        )}

        {/* Trial Balance Display */}
        {showStatement && trialBalanceData && (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase border-b">
                    Account Name
                  </th>
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase border-b">
                    Debit (Rs.)
                  </th>
                  <th className="px-6 py-3 text-xs font-medium tracking-wider text-right text-gray-500 uppercase border-b">
                    Credit (Rs.)
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {trialBalanceData.accounts.map((account, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                      {account.account_name}
                    </td>
                    <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                      {account.debit > 0 ? account.debit.toLocaleString() : '-'}
                    </td>
                    <td className="px-6 py-4 text-sm text-right text-gray-900 whitespace-nowrap">
                      {account.credit > 0 ? account.credit.toLocaleString() : '-'}
                    </td>
                  </tr>
                ))}

                {/* Total Row */}
                <tr className="bg-gray-100">
                  <td className="px-6 py-4 text-sm font-bold text-gray-900 whitespace-nowrap">
                    Total
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-right text-gray-900 whitespace-nowrap">
                    {trialBalanceData.totals.total_debit.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-right text-gray-900 whitespace-nowrap">
                    {trialBalanceData.totals.total_credit.toLocaleString()}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};
